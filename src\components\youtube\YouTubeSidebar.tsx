'use client';

import { useRef, useEffect } from 'react';
import { 
  HomeIcon,
  PlayIcon,
  ClockIcon,
  HandThumbUpIcon,
  FolderIcon,
  ChevronDownIcon,
  FireIcon,
  MusicalNoteIcon,
  FilmIcon,
  TvIcon,
  NewspaperIcon,
  TrophyIcon,
  AcademicCapIcon,
  CogIcon,
  FlagIcon,
  QuestionMarkCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';

interface YouTubeSidebarProps {
  isCollapsed: boolean;
}

export default function YouTubeSidebar({ isCollapsed }: YouTubeSidebarProps) {
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const savedScrollPosition = useRef<number>(0);

  // Save scroll position when collapsing, restore when expanding
  useEffect(() => {
    const container = scrollContainerRef.current;
    if (!container) return;

    if (isCollapsed) {
      // Save the current scroll position when collapsing
      savedScrollPosition.current = container.scrollTop;
      console.log('Saved scroll position:', savedScrollPosition.current);
    } else {
      // Restore the saved scroll position when expanding
      // Use multiple methods to ensure it works
      const restoreScroll = () => {
        if (container && savedScrollPosition.current > 0) {
          container.scrollTop = savedScrollPosition.current;
          console.log('Restored scroll position:', savedScrollPosition.current);
        }
      };

      // Try multiple times to ensure it works
      requestAnimationFrame(restoreScroll);
      setTimeout(restoreScroll, 0);
      setTimeout(restoreScroll, 50);
      setTimeout(restoreScroll, 100);
    }
  }, [isCollapsed]);

  const mainMenuItems = [
    { icon: HomeIcon, label: 'Home', active: true },
    { icon: PlayIcon, label: 'Shorts' },
    { icon: FolderIcon, label: 'Subscriptions' },
  ];

  const youMenuItems = [
    { icon: ClockIcon, label: 'History' },
    { icon: PlayIcon, label: 'Your videos' },
    { icon: ClockIcon, label: 'Watch later' },
    { icon: HandThumbUpIcon, label: 'Liked videos' },
  ];

  const exploreItems = [
    { icon: FireIcon, label: 'Trending' },
    { icon: MusicalNoteIcon, label: 'Music' },
    { icon: FilmIcon, label: 'Movies' },
    { icon: TvIcon, label: 'Live' },
    { icon: NewspaperIcon, label: 'News' },
    { icon: TrophyIcon, label: 'Sports' },
    { icon: AcademicCapIcon, label: 'Learning' },
  ];

  const subscriptions = [
    { name: 'Tech Channel', avatar: '🔧' },
    { name: 'Music World', avatar: '🎵' },
    { name: 'Gaming Hub', avatar: '🎮' },
    { name: 'Cooking Master', avatar: '👨‍🍳' },
    { name: 'Travel Vlog', avatar: '✈️' },
    { name: 'Science Today', avatar: '🔬' },
    { name: 'Art Studio', avatar: '🎨' },
    { name: 'Fitness Pro', avatar: '💪' },
  ];

  const settingsItems = [
    { icon: CogIcon, label: 'Settings' },
    { icon: FlagIcon, label: 'Report history' },
    { icon: QuestionMarkCircleIcon, label: 'Help' },
    { icon: ExclamationTriangleIcon, label: 'Send feedback' },
  ];

  return (
    <aside className={`bg-[#0f0f0f] border-r border-[#272727] flex-shrink-0 transition-all duration-300 ${
      isCollapsed ? 'w-[72px]' : 'w-[240px]'
    }`}>
      <div
        ref={scrollContainerRef}
        className="h-full overflow-y-auto scrollbar-youtube"
      >
        <div className="pt-4">
          {/* Main Menu */}
          <div className={isCollapsed ? "px-1" : "px-3"}>
            {mainMenuItems.map((item, index) => (
              <div
                key={index}
                className={`${
                  isCollapsed
                    ? 'flex flex-col items-center py-4 px-1 hover:bg-[#272727] cursor-pointer transition-colors'
                    : 'flex items-center space-x-6 px-3 py-2 rounded-lg hover:bg-[#272727] cursor-pointer transition-colors'
                } ${item.active ? 'bg-[#272727]' : ''}`}
              >
                <item.icon className="w-6 h-6 mb-1" />
                {isCollapsed ? (
                  <span className="text-xs text-center">{item.label}</span>
                ) : (
                  <span className="text-sm">{item.label}</span>
                )}
              </div>
            ))}
          </div>

          {!isCollapsed && (
            <>
              <hr className="border-[#272727] my-3" />

              {/* You Section */}
              <div className="px-3">
                <div className="flex items-center space-x-6 px-3 py-2">
                  <span className="text-sm font-medium">You</span>
                  <ChevronDownIcon className="w-4 h-4" />
                </div>
                {youMenuItems.map((item, index) => (
                  <div
                    key={index}
                    className="flex items-center space-x-6 px-3 py-2 rounded-lg hover:bg-[#272727] cursor-pointer transition-colors"
                  >
                    <item.icon className="w-6 h-6" />
                    <span className="text-sm">{item.label}</span>
                  </div>
                ))}
              </div>

              <hr className="border-[#272727] my-3" />

              {/* Subscriptions */}
              <div className="px-3">
                <div className="flex items-center space-x-6 px-3 py-2">
                  <span className="text-sm font-medium">Subscriptions</span>
                </div>
                {subscriptions.map((sub, index) => (
                  <div
                    key={index}
                    className="flex items-center space-x-6 px-3 py-2 rounded-lg hover:bg-[#272727] cursor-pointer transition-colors"
                  >
                    <div className="w-6 h-6 rounded-full bg-gray-600 flex items-center justify-center text-sm">
                      {sub.avatar}
                    </div>
                    <span className="text-sm">{sub.name}</span>
                  </div>
                ))}
              </div>

              <hr className="border-[#272727] my-3" />

              {/* Explore */}
              <div className="px-3">
                <div className="px-3 py-2">
                  <span className="text-sm font-medium">Explore</span>
                </div>
                {exploreItems.map((item, index) => (
                  <div
                    key={index}
                    className="flex items-center space-x-6 px-3 py-2 rounded-lg hover:bg-[#272727] cursor-pointer transition-colors"
                  >
                    <item.icon className="w-6 h-6" />
                    <span className="text-sm">{item.label}</span>
                  </div>
                ))}
              </div>

              <hr className="border-[#272727] my-3" />

              {/* Settings */}
              <div className="px-3 pb-4">
                {settingsItems.map((item, index) => (
                  <div
                    key={index}
                    className="flex items-center space-x-6 px-3 py-2 rounded-lg hover:bg-[#272727] cursor-pointer transition-colors"
                  >
                    <item.icon className="w-6 h-6" />
                    <span className="text-sm">{item.label}</span>
                  </div>
                ))}
              </div>
            </>
          )}
        </div>
      </div>
    </aside>
  );
}
