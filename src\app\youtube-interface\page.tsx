'use client';

import { useState } from 'react';
import YouTubeHeader from '@/components/youtube/YouTubeHeader';
import YouTubeSidebar from '@/components/youtube/YouTubeSidebar';
import YouTubeContent from '@/components/youtube/YouTubeContent';

export default function YouTubeInterface() {
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);

  const toggleSidebar = () => {
    setIsSidebarCollapsed(!isSidebarCollapsed);
  };

  return (
    <div className="h-screen bg-[#0f0f0f] text-white overflow-hidden">
      {/* Header */}
      <YouTubeHeader onToggleSidebar={toggleSidebar} />
      
      {/* Main Layout */}
      <div className="flex h-[calc(100vh-56px)]">
        {/* Sidebar */}
        <YouTubeSidebar isCollapsed={isSidebarCollapsed} />
        
        {/* Content Area */}
        <YouTubeContent isCollapsed={isSidebarCollapsed} />
      </div>
    </div>
  );
}
