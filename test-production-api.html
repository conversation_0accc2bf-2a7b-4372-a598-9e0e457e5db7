<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Production API Test - Tap2Go</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
        }
        .success { border-left: 4px solid #4CAF50; background: #f8fff8; }
        .error { border-left: 4px solid #f44336; background: #fff8f8; }
        .loading { border-left: 4px solid #2196F3; background: #f8f9ff; }
        pre {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 6px;
            overflow-x: auto;
            font-size: 12px;
        }
        button {
            background: #ff6b35;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 10px 10px 10px 0;
            font-size: 14px;
        }
        button:hover { background: #e55a2b; }
        button:disabled { background: #ccc; cursor: not-allowed; }
        .status { font-weight: bold; margin-bottom: 10px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Production API Diagnostic Tool</h1>
        <p>Professional debugging tool for Vercel production database issues</p>
        
        <div class="test-section">
            <h3>🌐 Environment Detection</h3>
            <div id="env-info">
                <p><strong>Current URL:</strong> <span id="current-url"></span></p>
                <p><strong>Environment:</strong> <span id="environment"></span></p>
                <p><strong>Timestamp:</strong> <span id="timestamp"></span></p>
            </div>
        </div>

        <div class="test-section">
            <h3>🔧 API Tests</h3>
            <button onclick="testDatabaseDebug()">Test Database Debug</button>
            <button onclick="testBlogPostsAPI()">Test Blog Posts API</button>
            <button onclick="testCreatePost()">Test Create Post</button>
            <button onclick="runAllTests()">Run All Tests</button>
        </div>

        <div id="test-results"></div>
    </div>

    <script>
        // Initialize environment info
        document.getElementById('current-url').textContent = window.location.href;
        document.getElementById('environment').textContent = 
            window.location.hostname.includes('vercel.app') ? 'Production (Vercel)' : 'Local Development';
        document.getElementById('timestamp').textContent = new Date().toISOString();

        function addTestResult(title, status, data, error = null) {
            const resultsDiv = document.getElementById('test-results');
            const testDiv = document.createElement('div');
            testDiv.className = `test-section ${status}`;
            
            let content = `
                <h4>${title}</h4>
                <div class="status">Status: ${status.toUpperCase()}</div>
            `;
            
            if (error) {
                content += `<p><strong>Error:</strong> ${error}</p>`;
            }
            
            if (data) {
                content += `<pre>${JSON.stringify(data, null, 2)}</pre>`;
            }
            
            testDiv.innerHTML = content;
            resultsDiv.appendChild(testDiv);
        }

        async function testDatabaseDebug() {
            addTestResult('Database Debug Test', 'loading', { message: 'Testing database connection...' });
            
            try {
                const response = await fetch('/api/debug/database');
                const data = await response.json();
                
                if (data.success) {
                    addTestResult('Database Debug Test', 'success', data.debug);
                } else {
                    addTestResult('Database Debug Test', 'error', data, data.error);
                }
            } catch (error) {
                addTestResult('Database Debug Test', 'error', null, error.message);
            }
        }

        async function testBlogPostsAPI() {
            addTestResult('Blog Posts API Test', 'loading', { message: 'Fetching blog posts...' });
            
            try {
                const response = await fetch('/api/blog/posts');
                const data = await response.json();
                
                if (data.success) {
                    addTestResult('Blog Posts API Test', 'success', {
                        postsCount: data.posts.length,
                        stats: data.stats,
                        samplePosts: data.posts.slice(0, 3)
                    });
                } else {
                    addTestResult('Blog Posts API Test', 'error', data, data.message);
                }
            } catch (error) {
                addTestResult('Blog Posts API Test', 'error', null, error.message);
            }
        }

        async function testCreatePost() {
            addTestResult('Create Post Test', 'loading', { message: 'Creating test post...' });
            
            const testPost = {
                title: `Production Test Post - ${new Date().toISOString()}`,
                content: 'This is a test post created from the production diagnostic tool.',
                excerpt: 'Production test excerpt',
                status: 'draft',
                author_name: 'Production Tester'
            };
            
            try {
                const response = await fetch('/api/blog/posts', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testPost)
                });
                
                const data = await response.json();
                
                if (data.success) {
                    addTestResult('Create Post Test', 'success', {
                        message: 'Post created successfully',
                        post: data.post
                    });
                } else {
                    addTestResult('Create Post Test', 'error', data, data.message);
                }
            } catch (error) {
                addTestResult('Create Post Test', 'error', null, error.message);
            }
        }

        async function runAllTests() {
            document.getElementById('test-results').innerHTML = '';
            await testDatabaseDebug();
            await new Promise(resolve => setTimeout(resolve, 1000));
            await testBlogPostsAPI();
            await new Promise(resolve => setTimeout(resolve, 1000));
            await testCreatePost();
        }
    </script>
</body>
</html>
