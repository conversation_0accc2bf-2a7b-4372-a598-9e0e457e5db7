'use client';

interface YouTubeContentProps {
  isCollapsed: boolean;
}

export default function YouTubeContent({ isCollapsed }: YouTubeContentProps) {
  const categories = [
    'All', 'Music', 'Gaming', 'News', 'Live', 'Sports', 'Learning', 'Fashion', 'Podcasts'
  ];

  const dummyVideos = [
    {
      id: 1,
      title: 'Amazing Nature Documentary - Wildlife in 4K',
      channel: 'Nature Explorer',
      views: '2.3M views',
      time: '2 weeks ago',
      duration: '15:42',
      thumbnail: 'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=400&h=225&fit=crop'
    },
    {
      id: 2,
      title: 'Learn React in 30 Minutes - Complete Tutorial',
      channel: 'Code Academy',
      views: '856K views',
      time: '1 week ago',
      duration: '30:15',
      thumbnail: 'https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=400&h=225&fit=crop'
    },
    {
      id: 3,
      title: 'Epic Gaming Moments - Best Highlights 2024',
      channel: 'Gaming Central',
      views: '1.2M views',
      time: '3 days ago',
      duration: '12:33',
      thumbnail: 'https://images.unsplash.com/photo-1511512578047-dfb367046420?w=400&h=225&fit=crop'
    },
    {
      id: 4,
      title: 'Cooking Masterclass: Perfect Pasta Every Time',
      channel: 'Chef\'s Kitchen',
      views: '445K views',
      time: '5 days ago',
      duration: '18:27',
      thumbnail: 'https://images.unsplash.com/photo-1551782450-17144efb9c50?w=400&h=225&fit=crop'
    },
    {
      id: 5,
      title: 'Travel Vlog: Hidden Gems in Japan',
      channel: 'Wanderlust Adventures',
      views: '678K views',
      time: '1 week ago',
      duration: '22:14',
      thumbnail: 'https://images.unsplash.com/photo-1493976040374-85c8e12f0c0e?w=400&h=225&fit=crop'
    },
    {
      id: 6,
      title: 'Latest Tech Reviews - Smartphones 2024',
      channel: 'Tech Insider',
      views: '923K views',
      time: '4 days ago',
      duration: '16:58',
      thumbnail: 'https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=400&h=225&fit=crop'
    },
    {
      id: 7,
      title: 'Fitness Workout: 20 Min Full Body HIIT',
      channel: 'FitLife Pro',
      views: '334K views',
      time: '2 days ago',
      duration: '20:00',
      thumbnail: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=225&fit=crop'
    },
    {
      id: 8,
      title: 'Music Production Tutorial - Beat Making',
      channel: 'Sound Studio',
      views: '567K views',
      time: '6 days ago',
      duration: '25:43',
      thumbnail: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=400&h=225&fit=crop'
    },
    {
      id: 9,
      title: 'Art Tutorial: Digital Painting Techniques',
      channel: 'Creative Canvas',
      views: '289K views',
      time: '1 week ago',
      duration: '35:12',
      thumbnail: 'https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?w=400&h=225&fit=crop'
    },
    {
      id: 10,
      title: 'Science Explained: Quantum Physics Basics',
      channel: 'Science Today',
      views: '1.1M views',
      time: '3 weeks ago',
      duration: '28:36',
      thumbnail: 'https://images.unsplash.com/photo-1635070041078-e363dbe005cb?w=400&h=225&fit=crop'
    },
    {
      id: 11,
      title: 'Home Improvement: DIY Kitchen Renovation',
      channel: 'Home Makeover',
      views: '412K views',
      time: '1 week ago',
      duration: '42:18',
      thumbnail: 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=225&fit=crop'
    },
    {
      id: 12,
      title: 'Photography Tips: Portrait Lighting Setup',
      channel: 'Photo Pro',
      views: '198K views',
      time: '4 days ago',
      duration: '14:29',
      thumbnail: 'https://images.unsplash.com/photo-1502920917128-1aa500764cbd?w=400&h=225&fit=crop'
    }
  ];

  return (
    <main className={`flex-1 bg-[#0f0f0f] transition-all duration-300 ${isCollapsed ? 'ml-0' : 'ml-0'}`}>
      <div className="pt-4">
        {/* Category Pills */}
        <div className="px-6 mb-6">
          <div className="flex space-x-3 overflow-x-auto scrollbar-none">
            {categories.map((category, index) => (
              <button
                key={index}
                className={`px-3 py-1.5 rounded-lg text-sm font-medium whitespace-nowrap transition-colors ${
                  index === 0 
                    ? 'bg-white text-black' 
                    : 'bg-[#272727] text-white hover:bg-[#3f3f3f]'
                }`}
              >
                {category}
              </button>
            ))}
          </div>
        </div>

        {/* Video Grid */}
        <div className="px-6">
          <div className={`grid gap-4 ${
            isCollapsed 
              ? 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5' 
              : 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4'
          }`}>
            {dummyVideos.map((video) => (
              <div key={video.id} className="cursor-pointer group">
                {/* Thumbnail */}
                <div className="relative mb-3">
                  <img
                    src={video.thumbnail}
                    alt={video.title}
                    className="w-full aspect-video object-cover rounded-lg group-hover:rounded-none transition-all duration-200"
                  />
                  <div className="absolute bottom-2 right-2 bg-black bg-opacity-80 text-white text-xs px-1.5 py-0.5 rounded">
                    {video.duration}
                  </div>
                </div>

                {/* Video Info */}
                <div className="flex space-x-3">
                  <div className="w-9 h-9 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold text-sm flex-shrink-0">
                    {video.channel.charAt(0)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <h3 className="text-white text-sm font-medium line-clamp-2 mb-1 group-hover:text-gray-300">
                      {video.title}
                    </h3>
                    <p className="text-gray-400 text-xs mb-1 hover:text-white cursor-pointer">
                      {video.channel}
                    </p>
                    <p className="text-gray-400 text-xs">
                      {video.views} • {video.time}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </main>
  );
}
