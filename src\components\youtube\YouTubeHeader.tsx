'use client';

import { useState } from 'react';
import { 
  Bars3Icon, 
  MagnifyingGlassIcon, 
  MicrophoneIcon,
  VideoCameraIcon,
  BellIcon,
  UserCircleIcon
} from '@heroicons/react/24/outline';

interface YouTubeHeaderProps {
  onToggleSidebar: () => void;
}

export default function YouTubeHeader({ onToggleSidebar }: YouTubeHeaderProps) {
  const [searchQuery, setSearchQuery] = useState('');

  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-[#0f0f0f] border-b border-[#272727] h-14">
      <div className="flex items-center justify-between px-4 h-full">
        {/* Left Section - Menu & Logo */}
        <div className="flex items-center space-x-4">
          <button
            onClick={onToggleSidebar}
            className="p-2 hover:bg-[#272727] rounded-full transition-colors"
            aria-label="Toggle sidebar"
          >
            <Bars3Icon className="w-6 h-6" />
          </button>
          
          <div className="flex items-center space-x-1">
            <div className="w-8 h-8 bg-red-600 rounded flex items-center justify-center">
              <svg className="w-5 h-5 text-white" viewBox="0 0 24 24" fill="currentColor">
                <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
              </svg>
            </div>
            <span className="text-xl font-semibold">YouTube</span>
          </div>
        </div>

        {/* Center Section - Search */}
        <div className="flex-1 max-w-2xl mx-8">
          <div className="flex items-center">
            <div className="flex-1 flex">
              <input
                type="text"
                placeholder="Search"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full px-4 py-2 bg-[#121212] border border-[#303030] rounded-l-full text-white placeholder-gray-400 focus:outline-none focus:border-blue-500"
              />
              <button className="px-6 py-2 bg-[#303030] border border-[#303030] border-l-0 rounded-r-full hover:bg-[#404040] transition-colors">
                <MagnifyingGlassIcon className="w-5 h-5" />
              </button>
            </div>
            <button className="ml-4 p-2 bg-[#181818] hover:bg-[#272727] rounded-full transition-colors">
              <MicrophoneIcon className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Right Section - Actions & Profile */}
        <div className="flex items-center space-x-2">
          <button className="p-2 hover:bg-[#272727] rounded-full transition-colors">
            <VideoCameraIcon className="w-6 h-6" />
          </button>
          <button className="p-2 hover:bg-[#272727] rounded-full transition-colors relative">
            <BellIcon className="w-6 h-6" />
            <span className="absolute -top-1 -right-1 w-4 h-4 bg-red-600 rounded-full text-xs flex items-center justify-center">
              3
            </span>
          </button>
          <button className="p-1 hover:bg-[#272727] rounded-full transition-colors">
            <UserCircleIcon className="w-8 h-8" />
          </button>
        </div>
      </div>
    </header>
  );
}
